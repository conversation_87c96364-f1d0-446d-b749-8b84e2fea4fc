global:
  namespace: "onebank"

  # podSecurityContext:
  #   runAsNonRoot: true
  #   runAsUser: 1000
  #   runAsGroup: 1000
  #   fsGroup: 1000

  # securityContext:
  #   runAsNonRoot: true
  #   runAsUser: 1000
  #   runAsGroup: 1000

  # Default image host
  imageHost: "************.dkr.ecr.ap-southeast-1.amazonaws.com"

  # Default image pull policy
  imagePullPolicy: "IfNotPresent"

  initImage:
    repository: "{{ .Values.global.imageHost }}/onebank/busybox"
    tag: "latest"
    pullPolicy: "IfNotPresent"

  config:
    service:
      host: "http://tellerx-venus-core-configservice.onebank:8888/"

  middleware:
    defaultPlatform:
      nacos:
        # serverAddr: "nacosx-headless.paas.svc.cluster.local:8848"
        serverAddr: "tellerx-nacos-server.paas:8848"
        username: nacos
        password: nacos
      kafka:
        service: kafka.galaxy:9092
      redis:
        database: 0
        host: "redis-standalone-redis.paas"
        port: 6379
        password: ""
        lettuce:
          pool:
            maxActive: 20
            maxWait: 3000
            maxIdle: 5
            minIdle: 0
          timeout: 15000

  venus:
    digitalsignature: true
    mq:
      enable: false
      type: rabbitmq
      producer:
        namesrvAddr: localhost:5672
        userName: "guest"
        password: "guest"
      consumer:
        namesrvAddr: localhost:5672
        userName: "guest"
        password: "guest"

  eureka:
    client:
      enabled: false
      registerWithEureka: true
      fetchRegistry: true
      serviceUrl:
        defaultZone: "http://localhost:7071/eureka/"
      healthcheck:
        enabled: false

operationsm-receipt:
  enabled: false
venus-app-auth:
  enabled: false
venus-app-custom:
  enabled: false
venus-app-depn:
  enabled: false
venus-app-pmw:
  enabled: false
venus-app-sysparam:
  enabled: false
venus-app-user:
  enabled: false
venus-core-configservice:
  enabled: true
venus-core-environment:
  enabled: false
venus-core-gateway:
  enabled: true
venus-core-journal:
  enabled: false
venus-core-scenecontrol:
  enabled: false
venus-core-taskcontrol:
  enabled: false
openfire:
  enabled: false
simulator:
  enabled: false
