applicationName: venus-environ

profile: "dev"

replicaCount: 1

image:
  repository: "{{ .Values.global.imageHost }}/onebank/{{ .Chart.Name }}"
  tag: "0.0.5-alpha"

# Filebeat 配置
filebeat:
  image:
    repository: "{{ .Values.global.imageHost }}/galaxy/filebeat"
    tag: ********
    pullPolicy: IfNotPresent
  resources:
    limits:
      cpu: 500m
      memory: 500Mi
    requests:
      cpu: 30m
      memory: 500Mi

service:
  port: 11118
  type: "ClusterIP"
  # ipAddress: localhost

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

log:
  mountPath: /data/app/logs/{{ .Values.applicationName }}
  hostPath: /home/<USER>/logs/{{ .Values.applicationName }}

conf:
  - name: application
    mountPath: /data/app/config/application.yml
    subPath: application.yml
  - name: bootstrap
    mountPath: /data/app/config/bootstrap.yml
    subPath: bootstrap.yml

autoscaling:
  enabled: false

ingress:
  enabled: true
  hosts: []

# Volumes 配置
volumes:
  - name: logfile
    emptyDir: {}
